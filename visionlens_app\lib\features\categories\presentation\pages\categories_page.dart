import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';

/// صفحة الفئات
class CategoriesPage extends StatelessWidget {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final categories = [
      {
        'name': 'عدسات يومية',
        'description': 'عدسات لاصقة للاستخدام اليومي',
        'icon': Icons.today,
        'color': AppColors.primary,
        'count': 45,
      },
      {
        'name': 'عدسات أسبوعية',
        'description': 'عدسات لاصقة للاستخدام الأسبوعي',
        'icon': Icons.date_range,
        'color': AppColors.secondary,
        'count': 28,
      },
      {
        'name': 'عدسات شهرية',
        'description': 'عدسات لاصقة للاستخدام الشهري',
        'icon': Icons.calendar_month,
        'color': AppColors.accent,
        'count': 67,
      },
      {
        'name': 'عدسات ملونة',
        'description': 'عدسات لاصقة ملونة تجميلية',
        'icon': Icons.palette,
        'color': Colors.purple,
        'count': 89,
      },
      {
        'name': 'عدسات طبية',
        'description': 'عدسات لاصقة لتصحيح النظر',
        'icon': Icons.medical_services,
        'color': Colors.green,
        'count': 156,
      },
      {
        'name': 'عدسات للاستجماتيزم',
        'description': 'عدسات خاصة لتصحيح الاستجماتيزم',
        'icon': Icons.remove_red_eye,
        'color': Colors.orange,
        'count': 34,
      },
      {
        'name': 'عدسات متعددة البؤر',
        'description': 'عدسات لتصحيح قصر النظر الشيخوخي',
        'icon': Icons.visibility,
        'color': Colors.teal,
        'count': 23,
      },
      {
        'name': 'محاليل العناية',
        'description': 'محاليل تنظيف وحفظ العدسات',
        'icon': Icons.local_pharmacy,
        'color': Colors.blue,
        'count': 42,
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.categories,
          style: TextStyle(
            fontSize: AppDimensions.fontLg,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              // فتح صفحة البحث
            },
            icon: const Icon(Icons.search),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(AppDimensions.md),
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppDimensions.md,
            mainAxisSpacing: AppDimensions.md,
            childAspectRatio: 0.85,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return _buildCategoryCard(category);
          },
        ),
      ),
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return GestureDetector(
      onTap: () {
        // الانتقال لصفحة منتجات الفئة
      },
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLg),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الفئة
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: (category['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
              ),
              child: Icon(
                category['icon'] as IconData,
                size: AppDimensions.iconLg,
                color: category['color'] as Color,
              ),
            ),

            SizedBox(height: AppDimensions.verticalMd),

            // اسم الفئة
            Text(
              category['name'] as String,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: AppDimensions.fontMd,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            SizedBox(height: AppDimensions.verticalSm),

            // وصف الفئة
            Text(
              category['description'] as String,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: AppDimensions.fontXs,
                color: AppColors.textSecondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            SizedBox(height: AppDimensions.verticalSm),

            // عدد المنتجات
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.sm,
                vertical: AppDimensions.xs,
              ),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
              ),
              child: Text(
                '${category['count']} منتج',
                style: TextStyle(
                  fontSize: AppDimensions.fontXs,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
