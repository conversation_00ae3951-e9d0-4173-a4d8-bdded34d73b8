import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:visionlens_app/app_properties.dart';
import 'package:visionlens_app/models/contact_lens.dart';
import 'package:visionlens_app/custom_background.dart';

class ProductList extends StatelessWidget {
  final List<ContactLens> products;
  final bool isGridView;
  final VoidCallback? onLoadMore;

  const ProductList({
    Key? key,
    required this.products,
    this.isGridView = true,
    this.onLoadMore,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return _buildEmptyState();
    }

    return isGridView ? _buildGridView() : _buildListView();
  }

  Widget _buildGridView() {
    return Padding(
      padding: const EdgeInsets.all(spacingM),
      child: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: spacingM,
        crossAxisSpacing: spacingM,
        itemCount: products.length,
        itemBuilder: (context, index) {
          return ProductCard(
            product: products[index],
            onTap: () => _onProductTap(products[index]),
            onAddToCart: () => _onAddToCart(products[index]),
            onToggleFavorite: () => _onToggleFavorite(products[index]),
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(spacingM),
      itemCount: products.length,
      itemBuilder: (context, index) {
        return ProductListTile(
          product: products[index],
          onTap: () => _onProductTap(products[index]),
          onAddToCart: () => _onAddToCart(products[index]),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.visibility_off,
            size: 80,
            color: textSecondary,
          ),
          const SizedBox(height: spacingL),
          Text(
            'لا توجد منتجات',
            style: TextStyle(
              fontSize: fontXL,
              fontWeight: FontWeight.bold,
              color: textPrimary,
            ),
          ),
          const SizedBox(height: spacingS),
          Text(
            'لم نجد أي منتجات تطابق بحثك',
            style: TextStyle(
              fontSize: fontM,
              color: textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  void _onProductTap(ContactLens product) {
    // Navigate to product details
    print('Product tapped: ${product.name}');
  }

  void _onAddToCart(ContactLens product) {
    // Add to cart logic
    print('Added to cart: ${product.name}');
  }

  void _onToggleFavorite(ContactLens product) {
    // Toggle favorite logic
    print('Toggled favorite: ${product.name}');
  }
}

class ProductCard extends StatelessWidget {
  final ContactLens product;
  final VoidCallback onTap;
  final VoidCallback onAddToCart;
  final VoidCallback onToggleFavorite;

  const ProductCard({
    Key? key,
    required this.product,
    required this.onTap,
    required this.onAddToCart,
    required this.onToggleFavorite,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(radiusLarge),
          boxShadow: cardShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProductImage(),
            _buildProductInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildProductImage() {
    return Container(
      height: 160,
      decoration: BoxDecoration(
        color: backgroundGrey,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(radiusLarge),
          topRight: Radius.circular(radiusLarge),
        ),
      ),
      child: Stack(
        children: [
          // Product Image
          Center(
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: primaryBlue.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.visibility,
                size: 40,
                color: primaryBlue,
              ),
            ),
          ),
          
          // Discount Badge
          if (product.hasDiscount)
            Positioned(
              top: spacingS,
              left: spacingS,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: spacingS,
                  vertical: spacingXS,
                ),
                decoration: BoxDecoration(
                  color: errorRed,
                  borderRadius: BorderRadius.circular(radiusSmall),
                ),
                child: Text(
                  '${product.discountPercentage.toInt()}%',
                  style: const TextStyle(
                    fontSize: fontXS,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          
          // Favorite Button
          Positioned(
            top: spacingS,
            right: spacingS,
            child: GestureDetector(
              onTap: onToggleFavorite,
              child: Container(
                padding: const EdgeInsets.all(spacingS),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: softShadow,
                ),
                child: const Icon(
                  Icons.favorite_outline,
                  size: 20,
                  color: textSecondary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductInfo() {
    return Padding(
      padding: const EdgeInsets.all(spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Name
          Text(
            product.name,
            style: const TextStyle(
              fontSize: fontM,
              fontWeight: FontWeight.bold,
              color: textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: spacingXS),
          
          // Brand
          Text(
            product.brand,
            style: const TextStyle(
              fontSize: fontS,
              color: textSecondary,
            ),
          ),
          const SizedBox(height: spacingS),
          
          // Rating
          Row(
            children: [
              ...List.generate(5, (index) {
                return Icon(
                  index < product.rating.floor() 
                      ? Icons.star 
                      : Icons.star_outline,
                  size: 16,
                  color: warningOrange,
                );
              }),
              const SizedBox(width: spacingXS),
              Text(
                '${product.rating}',
                style: const TextStyle(
                  fontSize: fontXS,
                  color: textSecondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                ' (${product.reviewCount})',
                style: const TextStyle(
                  fontSize: fontXS,
                  color: textSecondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: spacingS),
          
          // Price and Add to Cart
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.formattedPrice,
                      style: const TextStyle(
                        fontSize: fontM,
                        fontWeight: FontWeight.bold,
                        color: primaryBlue,
                      ),
                    ),
                    if (product.hasDiscount)
                      Text(
                        product.formattedOriginalPrice,
                        style: const TextStyle(
                          fontSize: fontS,
                          color: textSecondary,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: onAddToCart,
                child: Container(
                  padding: const EdgeInsets.all(spacingS),
                  decoration: BoxDecoration(
                    color: primaryBlue,
                    borderRadius: BorderRadius.circular(radiusSmall),
                  ),
                  child: const Icon(
                    Icons.add_shopping_cart,
                    size: 20,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ProductListTile extends StatelessWidget {
  final ContactLens product;
  final VoidCallback onTap;
  final VoidCallback onAddToCart;

  const ProductListTile({
    Key? key,
    required this.product,
    required this.onTap,
    required this.onAddToCart,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(radiusMedium),
        boxShadow: cardShadow,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(spacingM),
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(radiusMedium),
          ),
          child: const Icon(
            Icons.visibility,
            color: primaryBlue,
          ),
        ),
        title: Text(
          product.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(product.brand),
            const SizedBox(height: spacingXS),
            Text(
              product.formattedPrice,
              style: const TextStyle(
                color: primaryBlue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: IconButton(
          onPressed: onAddToCart,
          icon: const Icon(Icons.add_shopping_cart),
          color: primaryBlue,
        ),
        onTap: onTap,
      ),
    );
  }
}
