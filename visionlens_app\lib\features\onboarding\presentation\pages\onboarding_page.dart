import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';
import 'package:visionlens_app/core/routing/app_routes.dart';

/// شاشات الترحيب للمستخدمين الجدد
class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingItem> _onboardingItems = [
    OnboardingItem(
      icon: Icons.visibility,
      title: 'مرحباً بك في عدستي',
      description: 'اكتشف أفضل مجموعة من العدسات اللاصقة عالية الجودة',
    ),
    OnboardingItem(
      icon: Icons.shopping_cart,
      title: 'تسوق بسهولة',
      description: 'تصفح واختر من مجموعة واسعة من العدسات بأفضل الأسعار',
    ),
    OnboardingItem(
      icon: Icons.local_shipping,
      title: 'توصيل سريع',
      description: 'احصل على طلبك في أسرع وقت مع خدمة التوصيل المجاني',
    ),
    OnboardingItem(
      icon: Icons.support_agent,
      title: 'دعم متميز',
      description: 'فريق خدمة العملاء متاح دائماً لمساعدتك',
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _onboardingItems.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  void _skipOnboarding() {
    _finishOnboarding();
  }

  void _finishOnboarding() {
    context.go(AppRoutes.login);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // زر التخطي
            Padding(
              padding: EdgeInsets.all(AppDimensions.md),
              child: Align(
                alignment: Alignment.topLeft,
                child: TextButton(
                  onPressed: _skipOnboarding,
                  child: Text(
                    AppStrings.skip,
                    style: TextStyle(
                      fontSize: AppDimensions.fontMd,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
            ),

            // محتوى الصفحات
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _onboardingItems.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(_onboardingItems[index]);
                },
              ),
            ),

            // مؤشرات الصفحات
            _buildPageIndicators(),

            SizedBox(height: AppDimensions.verticalLg),

            // أزرار التنقل
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // زر السابق
                  if (_currentPage > 0)
                    TextButton(
                      onPressed: () {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Text(
                        AppStrings.previous,
                        style: TextStyle(
                          fontSize: AppDimensions.fontMd,
                          color: AppColors.primary,
                        ),
                      ),
                    )
                  else
                    const SizedBox.shrink(),

                  // زر التالي/البدء
                  ElevatedButton(
                    onPressed: _nextPage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.surface,
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.lg,
                        vertical: AppDimensions.sm,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
                      ),
                    ),
                    child: Text(
                      _currentPage == _onboardingItems.length - 1
                          ? 'ابدأ الآن'
                          : AppStrings.next,
                      style: TextStyle(
                        fontSize: AppDimensions.fontMd,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.verticalLg),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(OnboardingItem item) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.lg),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // الأيقونة
          Container(
            width: 120.w,
            height: 120.w,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(AppDimensions.radiusXl),
            ),
            child: Icon(
              item.icon,
              size: AppDimensions.iconXxl * 1.5,
              color: AppColors.surface,
            ),
          ),

          SizedBox(height: AppDimensions.verticalXl),

          // العنوان
          Text(
            item.title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: AppDimensions.fontTitle,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),

          SizedBox(height: AppDimensions.verticalMd),

          // الوصف
          Text(
            item.description,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: AppDimensions.fontLg,
              color: AppColors.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _onboardingItems.length,
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: AppDimensions.xs),
          width: _currentPage == index ? 24.w : 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? AppColors.primary
                : AppColors.grey300,
            borderRadius: BorderRadius.circular(AppDimensions.radiusXs),
          ),
        ),
      ),
    );
  }
}

/// عنصر شاشة الترحيب
class OnboardingItem {
  final IconData icon;
  final String title;
  final String description;

  OnboardingItem({
    required this.icon,
    required this.title,
    required this.description,
  });
}
