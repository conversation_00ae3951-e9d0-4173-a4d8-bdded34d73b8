import 'package:flutter/material.dart';
import 'app_properties.dart';

class MainBackground extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final height = size.height;
    final width = size.width;
    
    Paint paint = Paint();

    // Create gradient background
    paint.shader = LinearGradient(
      colors: [
        primaryBlue.withOpacity(0.1),
        accentTeal.withOpacity(0.05),
        Colors.white,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ).createShader(Rect.fromLTWH(0, 0, width, height));

    // Draw background
    canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paint);

    // Add decorative circles
    paint.shader = null;
    paint.color = primaryBlue.withOpacity(0.03);
    
    // Large circle top right
    canvas.drawCircle(
      Offset(width * 0.8, height * 0.1),
      width * 0.3,
      paint,
    );
    
    // Medium circle bottom left
    paint.color = accentTeal.withOpacity(0.02);
    canvas.drawCircle(
      Offset(width * 0.2, height * 0.8),
      width * 0.2,
      paint,
    );
    
    // Small circle center
    paint.color = lightBlue.withOpacity(0.04);
    canvas.drawCircle(
      Offset(width * 0.6, height * 0.5),
      width * 0.15,
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class CardBackground extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final height = size.height;
    final width = size.width;
    
    Paint paint = Paint();

    // Create subtle gradient for cards
    paint.shader = LinearGradient(
      colors: [
        Colors.white,
        backgroundGrey.withOpacity(0.3),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ).createShader(Rect.fromLTWH(0, 0, width, height));

    // Draw rounded rectangle
    RRect roundedRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, width, height),
      const Radius.circular(radiusMedium),
    );
    
    canvas.drawRRect(roundedRect, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class ProductCardBackground extends CustomPainter {
  final Color primaryColor;
  final Color secondaryColor;

  ProductCardBackground({
    this.primaryColor = primaryBlue,
    this.secondaryColor = lightBlue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final height = size.height;
    final width = size.width;
    
    Paint paint = Paint();

    // Create product card gradient
    paint.shader = LinearGradient(
      colors: [
        primaryColor.withOpacity(0.05),
        secondaryColor.withOpacity(0.02),
        Colors.white,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ).createShader(Rect.fromLTWH(0, 0, width, height));

    // Draw rounded rectangle
    RRect roundedRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, width, height),
      const Radius.circular(radiusLarge),
    );
    
    canvas.drawRRect(roundedRect, paint);

    // Add subtle border
    paint.shader = null;
    paint.color = primaryColor.withOpacity(0.1);
    paint.style = PaintingStyle.stroke;
    paint.strokeWidth = 1.0;
    
    canvas.drawRRect(roundedRect, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class WaveBackground extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final height = size.height;
    final width = size.width;
    
    Paint paint = Paint();

    // Create wave gradient
    paint.shader = LinearGradient(
      colors: [
        primaryBlue,
        accentTeal,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ).createShader(Rect.fromLTWH(0, 0, width, height));

    // Create wave path
    Path path = Path();
    path.moveTo(0, height * 0.7);
    
    // First wave
    path.quadraticBezierTo(
      width * 0.25, height * 0.6,
      width * 0.5, height * 0.7,
    );
    
    // Second wave
    path.quadraticBezierTo(
      width * 0.75, height * 0.8,
      width, height * 0.7,
    );
    
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
