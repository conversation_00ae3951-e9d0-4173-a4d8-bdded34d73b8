# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      url: "https://pub.dartlang.org"
    source: hosted
    version: "22.0.0"
  after_layout:
    dependency: transitive
    description:
      name: after_layout
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.2"
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.6"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  build:
    dependency: transitive
    description:
      name: build
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.4"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      url: "https://pub.dartlang.org"
    source: hosted
    version: "7.2.2"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.1.3"
  card_swiper:
    dependency: "direct main"
    description:
      name: card_swiper
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.4"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  cli_util:
    dependency: transitive
    description:
      name: cli_util
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.3.5"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.16.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  country_code_picker:
    dependency: "direct main"
    description:
      name: country_code_picker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.2"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  flare_flutter:
    dependency: transitive
    description:
      name: flare_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.2"
  flare_loading:
    dependency: transitive
    description:
      name: flare_loading
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.0"
  flare_splash_screen:
    dependency: "direct main"
    description:
      name: flare_splash_screen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_launcher_icons:
    dependency: "direct dev"
    description:
      name: flutter_launcher_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.9.2"
  flutter_rating_bar:
    dependency: "direct main"
    description:
      name: flutter_rating_bar
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.23.0+1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  http:
    dependency: "direct main"
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.4"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.8"
  infinite_listview:
    dependency: transitive
    description:
      name: infinite_listview
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.0"
  json_serializable:
    dependency: "direct dev"
    description:
      name: json_serializable
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.1.4"
  logging:
    dependency: transitive
    description:
      name: logging
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.11"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.1.4"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  modal_bottom_sheet:
    dependency: transitive
    description:
      name: modal_bottom_sheet
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  numberpicker:
    dependency: "direct main"
    description:
      name: numberpicker
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  package_config:
    dependency: transitive
    description:
      name: package_config
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.2"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.5.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.1"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  pin_code_text_field:
    dependency: "direct main"
    description:
      name: pin_code_text_field
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  pool:
    dependency: transitive
    description:
      name: pool
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.5.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  rubber:
    dependency: "direct main"
    description:
      name: rubber
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.3"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.2"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.9"
  timing:
    dependency: transitive
    description:
      name: timing
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  watcher:
    dependency: transitive
    description:
      name: watcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.3.1"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
sdks:
  dart: ">=2.17.0-0 <3.0.0"
  flutter: ">=1.24.0-7.0"
