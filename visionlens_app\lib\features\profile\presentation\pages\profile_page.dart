import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';

/// صفحة الملف الشخصي
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // معلومات المستخدم
              _buildUserInfo(),

              SizedBox(height: AppDimensions.verticalLg),

              // قائمة الخيارات
              _buildMenuOptions(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.lg),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.vertical(
          bottom: Radius.circular(AppDimensions.radiusXl),
        ),
      ),
      child: Column(
        children: [
          // صورة المستخدم
          Container(
            width: 100.w,
            height: 100.w,
            decoration: BoxDecoration(
              color: AppColors.surface,
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColors.surface,
                width: 4,
              ),
            ),
            child: Icon(
              Icons.person,
              size: AppDimensions.iconXxl,
              color: AppColors.primary,
            ),
          ),

          SizedBox(height: AppDimensions.verticalMd),

          // اسم المستخدم
          Text(
            'أحمد محمد علي',
            style: TextStyle(
              fontSize: AppDimensions.fontTitle,
              fontWeight: FontWeight.bold,
              color: AppColors.surface,
            ),
          ),

          SizedBox(height: AppDimensions.verticalSm),

          // البريد الإلكتروني
          Text(
            '<EMAIL>',
            style: TextStyle(
              fontSize: AppDimensions.fontMd,
              color: AppColors.surface.withOpacity(0.8),
            ),
          ),

          SizedBox(height: AppDimensions.verticalSm),

          // رقم الهاتف
          Text(
            '+964 770 123 4567',
            style: TextStyle(
              fontSize: AppDimensions.fontMd,
              color: AppColors.surface.withOpacity(0.8),
            ),
          ),

          SizedBox(height: AppDimensions.verticalLg),

          // زر تعديل الملف الشخصي
          ElevatedButton(
            onPressed: () {
              // الانتقال لصفحة تعديل الملف الشخصي
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.surface,
              foregroundColor: AppColors.primary,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.lg,
                vertical: AppDimensions.sm,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
              ),
            ),
            child: Text(
              'تعديل الملف الشخصي',
              style: TextStyle(
                fontSize: AppDimensions.fontMd,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuOptions(BuildContext context) {
    final menuItems = [
      {
        'icon': Icons.shopping_bag_outlined,
        'title': AppStrings.orders,
        'subtitle': 'عرض وتتبع طلباتك',
        'onTap': () {
          // الانتقال لصفحة الطلبات
        },
      },
      {
        'icon': Icons.location_on_outlined,
        'title': AppStrings.addresses,
        'subtitle': 'إدارة عناوين التوصيل',
        'onTap': () {
          // الانتقال لصفحة العناوين
        },
      },
      {
        'icon': Icons.payment_outlined,
        'title': 'طرق الدفع',
        'subtitle': 'إدارة بطاقاتك ووسائل الدفع',
        'onTap': () {
          // الانتقال لصفحة طرق الدفع
        },
      },
      {
        'icon': Icons.notifications_outlined,
        'title': AppStrings.notifications,
        'subtitle': 'إعدادات الإشعارات',
        'onTap': () {
          // الانتقال لصفحة الإشعارات
        },
      },
      {
        'icon': Icons.language_outlined,
        'title': AppStrings.language,
        'subtitle': 'العربية',
        'onTap': () {
          // الانتقال لصفحة اللغة
        },
      },
      {
        'icon': Icons.help_outline,
        'title': AppStrings.customerService,
        'subtitle': 'المساعدة والدعم',
        'onTap': () {
          // الانتقال لصفحة خدمة العملاء
        },
      },
      {
        'icon': Icons.info_outline,
        'title': AppStrings.aboutApp,
        'subtitle': 'معلومات التطبيق',
        'onTap': () {
          // الانتقال لصفحة حول التطبيق
        },
      },
      {
        'icon': Icons.privacy_tip_outlined,
        'title': AppStrings.privacyPolicy,
        'subtitle': 'سياسة الخصوصية',
        'onTap': () {
          // الانتقال لصفحة سياسة الخصوصية
        },
      },
      {
        'icon': Icons.description_outlined,
        'title': AppStrings.termsAndConditions,
        'subtitle': 'الشروط والأحكام',
        'onTap': () {
          // الانتقال لصفحة الشروط والأحكام
        },
      },
      {
        'icon': Icons.logout,
        'title': AppStrings.logout,
        'subtitle': 'تسجيل الخروج من التطبيق',
        'onTap': () => _showLogoutDialog(context),
        'isDestructive': true,
      },
    ];

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
      child: Column(
        children: menuItems.map((item) => _buildMenuItem(item)).toList(),
      ),
    );
  }

  Widget _buildMenuItem(Map<String, dynamic> item) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.sm),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(AppDimensions.sm),
          decoration: BoxDecoration(
            color: (item['isDestructive'] == true ? AppColors.error : AppColors.primary)
                .withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
          ),
          child: Icon(
            item['icon'] as IconData,
            color: item['isDestructive'] == true ? AppColors.error : AppColors.primary,
            size: AppDimensions.iconMd,
          ),
        ),
        title: Text(
          item['title'] as String,
          style: TextStyle(
            fontSize: AppDimensions.fontMd,
            fontWeight: FontWeight.w600,
            color: item['isDestructive'] == true 
                ? AppColors.error 
                : AppColors.textPrimary,
          ),
        ),
        subtitle: Text(
          item['subtitle'] as String,
          style: TextStyle(
            fontSize: AppDimensions.fontSm,
            color: AppColors.textSecondary,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: AppDimensions.iconSm,
          color: AppColors.grey400,
        ),
        onTap: item['onTap'] as VoidCallback,
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.md,
          vertical: AppDimensions.sm,
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          AppStrings.logout,
          style: TextStyle(
            fontSize: AppDimensions.fontLg,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              AppStrings.cancel,
              style: TextStyle(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // تنفيذ تسجيل الخروج
              _logout(context);
            },
            child: Text(
              AppStrings.logout,
              style: TextStyle(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _logout(BuildContext context) {
    // تنفيذ منطق تسجيل الخروج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تسجيل الخروج بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
    
    // الانتقال لشاشة تسجيل الدخول
    // context.go(AppRoutes.login);
  }
}
