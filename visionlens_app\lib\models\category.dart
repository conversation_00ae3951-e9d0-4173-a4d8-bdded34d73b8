import 'package:flutter/material.dart';

class LensCategory {
  final String id;
  final String name;
  final String nameEn;
  final String description;
  final String imageUrl;
  final IconData icon;
  final Color color;
  final int productCount;
  final bool isActive;
  final List<String> subCategories;

  LensCategory({
    required this.id,
    required this.name,
    required this.nameEn,
    required this.description,
    required this.imageUrl,
    required this.icon,
    required this.color,
    required this.productCount,
    this.isActive = true,
    required this.subCategories,
  });

  factory LensCategory.fromJson(Map<String, dynamic> json) {
    return LensCategory(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      nameEn: json['nameEn'] ?? '',
      description: json['description'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      icon: _getIconFromString(json['icon'] ?? 'visibility'),
      color: Color(json['color'] ?? 0xFF2196F3),
      productCount: json['productCount'] ?? 0,
      isActive: json['isActive'] ?? true,
      subCategories: List<String>.from(json['subCategories'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'nameEn': nameEn,
      'description': description,
      'imageUrl': imageUrl,
      'icon': _getStringFromIcon(icon),
      'color': color.value,
      'productCount': productCount,
      'isActive': isActive,
      'subCategories': subCategories,
    };
  }

  static IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'visibility':
        return Icons.visibility;
      case 'remove_red_eye':
        return Icons.remove_red_eye;
      case 'lens':
        return Icons.lens;
      case 'circle':
        return Icons.circle;
      case 'colorize':
        return Icons.colorize;
      case 'palette':
        return Icons.palette;
      case 'schedule':
        return Icons.schedule;
      case 'calendar_today':
        return Icons.calendar_today;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      default:
        return Icons.visibility;
    }
  }

  static String _getStringFromIcon(IconData icon) {
    if (icon == Icons.visibility) return 'visibility';
    if (icon == Icons.remove_red_eye) return 'remove_red_eye';
    if (icon == Icons.lens) return 'lens';
    if (icon == Icons.circle) return 'circle';
    if (icon == Icons.colorize) return 'colorize';
    if (icon == Icons.palette) return 'palette';
    if (icon == Icons.schedule) return 'schedule';
    if (icon == Icons.calendar_today) return 'calendar_today';
    if (icon == Icons.star) return 'star';
    if (icon == Icons.favorite) return 'favorite';
    return 'visibility';
  }
}

// Predefined categories for VisionLens
class LensCategoryData {
  static List<LensCategory> getCategories() {
    return [
      LensCategory(
        id: '1',
        name: 'العدسات اليومية',
        nameEn: 'Daily Lenses',
        description: 'عدسات لاصقة للاستخدام اليومي',
        imageUrl: 'assets/images/daily_lenses.png',
        icon: Icons.schedule,
        color: const Color(0xFF4CAF50),
        productCount: 45,
        subCategories: ['شفافة', 'ملونة', 'طبية'],
      ),
      LensCategory(
        id: '2',
        name: 'العدسات الأسبوعية',
        nameEn: 'Weekly Lenses',
        description: 'عدسات لاصقة للاستخدام الأسبوعي',
        imageUrl: 'assets/images/weekly_lenses.png',
        icon: Icons.calendar_today,
        color: const Color(0xFF2196F3),
        productCount: 32,
        subCategories: ['شفافة', 'ملونة'],
      ),
      LensCategory(
        id: '3',
        name: 'العدسات الشهرية',
        nameEn: 'Monthly Lenses',
        description: 'عدسات لاصقة للاستخدام الشهري',
        imageUrl: 'assets/images/monthly_lenses.png',
        icon: Icons.calendar_view_month,
        color: const Color(0xFFFF9800),
        productCount: 28,
        subCategories: ['شفافة', 'ملونة', 'طبية'],
      ),
      LensCategory(
        id: '4',
        name: 'العدسات الملونة',
        nameEn: 'Colored Lenses',
        description: 'عدسات لاصقة ملونة للجمال',
        imageUrl: 'assets/images/colored_lenses.png',
        icon: Icons.palette,
        color: const Color(0xFFE91E63),
        productCount: 67,
        subCategories: ['طبيعية', 'زاهية', 'متدرجة'],
      ),
      LensCategory(
        id: '5',
        name: 'العدسات الطبية',
        nameEn: 'Prescription Lenses',
        description: 'عدسات لاصقة لتصحيح النظر',
        imageUrl: 'assets/images/prescription_lenses.png',
        icon: Icons.visibility,
        color: const Color(0xFF9C27B0),
        productCount: 89,
        subCategories: ['قصر النظر', 'طول النظر', 'الاستجماتيزم'],
      ),
      LensCategory(
        id: '6',
        name: 'العدسات المميزة',
        nameEn: 'Premium Lenses',
        description: 'عدسات لاصقة فاخرة ومميزة',
        imageUrl: 'assets/images/premium_lenses.png',
        icon: Icons.star,
        color: const Color(0xFFFFD700),
        productCount: 23,
        subCategories: ['سيليكون هيدروجيل', 'مضادة للجفاف'],
      ),
    ];
  }
}
