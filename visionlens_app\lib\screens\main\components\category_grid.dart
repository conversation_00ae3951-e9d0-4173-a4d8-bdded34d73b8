import 'package:flutter/material.dart';
import 'package:visionlens_app/app_properties.dart';
import 'package:visionlens_app/models/category.dart';

class CategoryGrid extends StatelessWidget {
  final List<LensCategory> categories;
  final Function(LensCategory)? onCategoryTap;
  final bool showProductCount;

  const CategoryGrid({
    Key? key,
    required this.categories,
    this.onCategoryTap,
    this.showProductCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.all(spacingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: spacingM,
        mainAxisSpacing: spacingM,
        childAspectRatio: 1.2,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        return CategoryCard(
          category: categories[index],
          onTap: () => onCategoryTap?.call(categories[index]),
          showProductCount: showProductCount,
        );
      },
    );
  }
}

class CategoryCard extends StatelessWidget {
  final LensCategory category;
  final VoidCallback? onTap;
  final bool showProductCount;

  const CategoryCard({
    Key? key,
    required this.category,
    this.onTap,
    this.showProductCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              category.color.withOpacity(0.1),
              category.color.withOpacity(0.05),
              Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(radiusLarge),
          boxShadow: cardShadow,
          border: Border.all(
            color: category.color.withOpacity(0.2),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(spacingM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Category Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: category.color.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(radiusLarge),
                ),
                child: Icon(
                  category.icon,
                  size: 30,
                  color: category.color,
                ),
              ),
              const SizedBox(height: spacingM),
              
              // Category Name
              Text(
                category.name,
                style: const TextStyle(
                  fontSize: fontM,
                  fontWeight: FontWeight.bold,
                  color: textPrimary,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (showProductCount) ...[
                const SizedBox(height: spacingXS),
                Text(
                  '${category.productCount} منتج',
                  style: TextStyle(
                    fontSize: fontS,
                    color: category.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class HorizontalCategoryList extends StatelessWidget {
  final List<LensCategory> categories;
  final Function(LensCategory)? onCategoryTap;
  final LensCategory? selectedCategory;

  const HorizontalCategoryList({
    Key? key,
    required this.categories,
    this.onCategoryTap,
    this.selectedCategory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: spacingM),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategory?.id == category.id;
          
          return Container(
            width: 80,
            margin: const EdgeInsets.only(right: spacingM),
            child: GestureDetector(
              onTap: () => onCategoryTap?.call(category),
              child: Column(
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? category.color 
                          : category.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(radiusLarge),
                      boxShadow: isSelected ? cardShadow : null,
                    ),
                    child: Icon(
                      category.icon,
                      size: 30,
                      color: isSelected 
                          ? Colors.white 
                          : category.color,
                    ),
                  ),
                  const SizedBox(height: spacingS),
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: fontXS,
                      fontWeight: isSelected 
                          ? FontWeight.bold 
                          : FontWeight.normal,
                      color: isSelected 
                          ? category.color 
                          : textSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class CategoryChip extends StatelessWidget {
  final LensCategory category;
  final bool isSelected;
  final VoidCallback? onTap;

  const CategoryChip({
    Key? key,
    required this.category,
    this.isSelected = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(right: spacingS),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingS,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? category.color 
              : category.color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(radiusLarge),
          border: Border.all(
            color: category.color,
            width: isSelected ? 0 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              category.icon,
              size: 16,
              color: isSelected 
                  ? Colors.white 
                  : category.color,
            ),
            const SizedBox(width: spacingXS),
            Text(
              category.name,
              style: TextStyle(
                fontSize: fontS,
                fontWeight: FontWeight.w600,
                color: isSelected 
                    ? Colors.white 
                    : category.color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CategoryFilterChips extends StatelessWidget {
  final List<LensCategory> categories;
  final List<LensCategory> selectedCategories;
  final Function(LensCategory)? onCategoryToggle;

  const CategoryFilterChips({
    Key? key,
    required this.categories,
    required this.selectedCategories,
    this.onCategoryToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: spacingM),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategories.contains(category);
          
          return CategoryChip(
            category: category,
            isSelected: isSelected,
            onTap: () => onCategoryToggle?.call(category),
          );
        },
      ),
    );
  }
}
