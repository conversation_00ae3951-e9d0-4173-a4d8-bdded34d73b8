import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/routing/app_router.dart';

void main() {
  runApp(const VisionLensApp());
}

/// تطبيق عدستي الرئيسي
class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812), // iPhone X design size
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp.router(
          title: AppStrings.appName,
          debugShowCheckedModeBanner: false,

          // إعداد نظام التوجيه
          routerConfig: AppRouter.router,

          // إعداد الثيم
          theme: ThemeData(
            useMaterial3: true,

            // نظام الألوان
            colorScheme: ColorScheme.fromSeed(
              seedColor: AppColors.primary,
              primary: AppColors.primary,
              secondary: AppColors.secondary,
              surface: AppColors.surface,

              error: AppColors.error,
            ),

            // الخطوط
            textTheme: GoogleFonts.cairoTextTheme(Theme.of(context).textTheme)
                .apply(
                  bodyColor: AppColors.textPrimary,
                  displayColor: AppColors.textPrimary,
                ),
          ),

          // دعم اللغة العربية
          locale: const Locale('ar', 'IQ'),
          supportedLocales: const [Locale('ar', 'IQ'), Locale('en', 'US')],
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
        );
      },
    );
  }
}
