import 'package:flutter/material.dart';

import 'package:visionlens_app/app_properties.dart';
import 'package:visionlens_app/screens/main/main_page.dart';

void main() {
  runApp(const VisionLensApp());
}

class VisionLensApp extends StatelessWidget {
  const VisionLensApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VisionLens - متجر العدسات اللاصقة',
      debugShowCheckedModeBanner: false,

      // Theme
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: primaryBlue,
        scaffoldBackgroundColor: backgroundGrey,

        // Color Scheme
        colorScheme: ColorScheme.fromSeed(
          seedColor: primaryBlue,
          primary: primaryBlue,
          secondary: accentTeal,
          surface: Colors.white,
          background: backgroundGrey,
          error: errorRed,
        ),

        useMaterial3: true,
      ),

      home: MainPage(),
    );
  }
}
