import 'package:flutter/material.dart';
import 'package:visionlens_app/app_properties.dart';

class CustomBottomBar extends StatelessWidget {
  final TabController controller;

  const CustomBottomBar({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Safe<PERSON><PERSON>(
        child: Container(
          height: 70,
          child: TabBar(
            controller: controller,
            indicator: const BoxDecoration(),
            labelColor: primaryBlue,
            unselectedLabelColor: textSecondary,
            labelStyle: const TextStyle(
              fontSize: fontXS,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: const TextStyle(
              fontSize: fontXS,
              fontWeight: FontWeight.normal,
            ),
            tabs: [
              _buildTab(
                icon: Icons.home_outlined,
                selectedIcon: Icons.home,
                label: 'الرئيسية',
                index: 0,
              ),
              _buildTab(
                icon: Icons.category_outlined,
                selectedIcon: Icons.category,
                label: 'الفئات',
                index: 1,
              ),
              _buildTab(
                icon: Icons.shopping_cart_outlined,
                selectedIcon: Icons.shopping_cart,
                label: 'السلة',
                index: 2,
              ),
              _buildTab(
                icon: Icons.person_outline,
                selectedIcon: Icons.person,
                label: 'الملف الشخصي',
                index: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTab({
    required IconData icon,
    required IconData selectedIcon,
    required String label,
    required int index,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final isSelected = controller.index == index;
        
        return Tab(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? primaryBlue.withOpacity(0.1) 
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(radiusMedium),
                ),
                child: Icon(
                  isSelected ? selectedIcon : icon,
                  size: 24,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class CustomBottomBarItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final Widget page;

  CustomBottomBarItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.page,
  });
}

// Alternative implementation with more customization
class AdvancedBottomBar extends StatelessWidget {
  final TabController controller;
  final List<CustomBottomBarItem> items;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? unselectedColor;

  const AdvancedBottomBar({
    Key? key,
    required this.controller,
    required this.items,
    this.backgroundColor,
    this.selectedColor,
    this.unselectedColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(radiusLarge),
          topRight: Radius.circular(radiusLarge),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -10),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 80,
          padding: const EdgeInsets.symmetric(horizontal: spacingM),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              
              return _buildAdvancedTab(
                item: item,
                index: index,
                isSelected: controller.index == index,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildAdvancedTab({
    required CustomBottomBarItem item,
    required int index,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => controller.animateTo(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(
          horizontal: spacingM,
          vertical: spacingS,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? (selectedColor ?? primaryBlue).withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(radiusLarge),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isSelected ? item.selectedIcon : item.icon,
                key: ValueKey(isSelected),
                size: isSelected ? 28 : 24,
                color: isSelected 
                    ? (selectedColor ?? primaryBlue)
                    : (unselectedColor ?? textSecondary),
              ),
            ),
            const SizedBox(height: 4),
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 200),
              style: TextStyle(
                fontSize: isSelected ? fontS : fontXS,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected 
                    ? (selectedColor ?? primaryBlue)
                    : (unselectedColor ?? textSecondary),
              ),
              child: Text(item.label),
            ),
          ],
        ),
      ),
    );
  }
}
