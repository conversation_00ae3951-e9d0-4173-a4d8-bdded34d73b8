import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:visionlens_app/core/routing/app_routes.dart';
import 'package:visionlens_app/features/splash/presentation/pages/splash_page.dart';
import 'package:visionlens_app/features/onboarding/presentation/pages/onboarding_page.dart';
import 'package:visionlens_app/features/auth/presentation/pages/login_page.dart';
import 'package:visionlens_app/features/auth/presentation/pages/register_page.dart';
import 'package:visionlens_app/features/home/<USER>/pages/main_page.dart';

/// إعداد نظام التوجيه للتطبيق
class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.splash,
    routes: [
      // شاشة البداية
      GoRoute(
        path: AppRoutes.splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // شاشات الترحيب
      GoRoute(
        path: AppRoutes.onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingPage(),
      ),

      // شاشات المصادقة
      GoRoute(
        path: AppRoutes.login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: AppRoutes.register,
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),

      // الشاشة الرئيسية مع التنقل السفلي
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const MainPage(),
      ),

      // مسارات المنتجات
      GoRoute(
        path: '${AppRoutes.productDetails}/:id',
        name: 'product-details',
        builder: (context, state) {
          final productId = state.pathParameters['id']!;
          return ProductDetailsPage(productId: productId);
        },
      ),

      // مسارات التسوق
      GoRoute(
        path: AppRoutes.cart,
        name: 'cart',
        builder: (context, state) => const CartPage(),
      ),
      GoRoute(
        path: AppRoutes.checkout,
        name: 'checkout',
        builder: (context, state) => const CheckoutPage(),
      ),

      // مسارات الملف الشخصي
      GoRoute(
        path: AppRoutes.profile,
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),
      GoRoute(
        path: AppRoutes.orders,
        name: 'orders',
        builder: (context, state) => const OrdersPage(),
      ),
      GoRoute(
        path: AppRoutes.wishlist,
        name: 'wishlist',
        builder: (context, state) => const WishlistPage(),
      ),

      // مسارات الإعدادات
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
      ),
    ],
    errorBuilder: (context, state) => const ErrorPage(),
  );
}

// صفحات مؤقتة للتطوير
class ProductDetailsPage extends StatelessWidget {
  final String productId;
  
  const ProductDetailsPage({super.key, required this.productId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('تفاصيل المنتج $productId')),
      body: const Center(child: Text('صفحة تفاصيل المنتج')),
    );
  }
}

class CartPage extends StatelessWidget {
  const CartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('سلة التسوق')),
      body: const Center(child: Text('صفحة سلة التسوق')),
    );
  }
}

class CheckoutPage extends StatelessWidget {
  const CheckoutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إتمام الطلب')),
      body: const Center(child: Text('صفحة إتمام الطلب')),
    );
  }
}

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الملف الشخصي')),
      body: const Center(child: Text('صفحة الملف الشخصي')),
    );
  }
}

class OrdersPage extends StatelessWidget {
  const OrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('طلباتي')),
      body: const Center(child: Text('صفحة الطلبات')),
    );
  }
}

class WishlistPage extends StatelessWidget {
  const WishlistPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المفضلة')),
      body: const Center(child: Text('صفحة المفضلة')),
    );
  }
}

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الإعدادات')),
      body: const Center(child: Text('صفحة الإعدادات')),
    );
  }
}

class ErrorPage extends StatelessWidget {
  const ErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: const Center(child: Text('حدث خطأ في التطبيق')),
    );
  }
}
