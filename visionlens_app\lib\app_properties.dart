import 'package:flutter/material.dart';

// VisionLens Color Palette
const Color primaryBlue = Color(0xff2196F3);
const Color lightBlue = Color(0xff64B5F6);
const Color darkBlue = Color(0xff1976D2);
const Color accentTeal = Color(0xff00BCD4);
const Color lightTeal = Color(0xff4DD0E1);

// Secondary Colors
const Color softGreen = Color(0xff4CAF50);
const Color lightGreen = Color(0xff81C784);
const Color warningOrange = Color(0xffFF9800);
const Color errorRed = Color(0xffF44336);

// Neutral Colors
const Color darkGrey = Color(0xff424242);
const Color mediumGrey = Color(0xff757575);
const Color lightGrey = Color(0xffE0E0E0);
const Color backgroundGrey = Color(0xffF5F5F5);

// Text Colors
const Color textPrimary = Color(0xff212121);
const Color textSecondary = Color(0xff757575);
const Color textLight = Color(0xffBDBDBD);

// Gradients
const LinearGradient primaryGradient = LinearGradient(
  colors: [
    primaryBlue,
    lightBlue,
    accentTeal,
  ],
  begin: FractionalOffset.topLeft,
  end: FractionalOffset.bottomRight,
);

const LinearGradient cardGradient = LinearGradient(
  colors: [
    Color(0xffFFFFFF),
    Color(0xffF8F9FA),
  ],
  begin: FractionalOffset.topCenter,
  end: FractionalOffset.bottomCenter,
);

const LinearGradient buttonGradient = LinearGradient(
  colors: [
    primaryBlue,
    darkBlue,
  ],
  begin: FractionalOffset.topCenter,
  end: FractionalOffset.bottomCenter,
);

// Shadows
const List<BoxShadow> cardShadow = [
  BoxShadow(
    color: Color.fromRGBO(0, 0, 0, 0.1),
    offset: Offset(0, 4),
    blurRadius: 12,
    spreadRadius: 0,
  )
];

const List<BoxShadow> buttonShadow = [
  BoxShadow(
    color: Color.fromRGBO(33, 150, 243, 0.3),
    offset: Offset(0, 6),
    blurRadius: 20,
    spreadRadius: 0,
  )
];

const List<BoxShadow> softShadow = [
  BoxShadow(
    color: Color.fromRGBO(0, 0, 0, 0.05),
    offset: Offset(0, 2),
    blurRadius: 8,
    spreadRadius: 0,
  )
];

// Border Radius
const double radiusSmall = 8.0;
const double radiusMedium = 12.0;
const double radiusLarge = 16.0;
const double radiusXLarge = 24.0;

// Spacing
const double spacingXS = 4.0;
const double spacingS = 8.0;
const double spacingM = 16.0;
const double spacingL = 24.0;
const double spacingXL = 32.0;
const double spacingXXL = 48.0;

// Font Sizes
const double fontXS = 12.0;
const double fontS = 14.0;
const double fontM = 16.0;
const double fontL = 18.0;
const double fontXL = 20.0;
const double fontXXL = 24.0;
const double fontHeading = 28.0;

// Screen-aware sizing function
double screenAwareSize(double size, BuildContext context) {
  double baseHeight = 812.0; // iPhone 12 Pro height
  return size * MediaQuery.of(context).size.height / baseHeight;
}

// Responsive width function
double screenWidth(BuildContext context) {
  return MediaQuery.of(context).size.width;
}

// Responsive height function
double screenHeight(BuildContext context) {
  return MediaQuery.of(context).size.height;
}

// Safe area padding
EdgeInsets safePadding(BuildContext context) {
  return MediaQuery.of(context).padding;
}
