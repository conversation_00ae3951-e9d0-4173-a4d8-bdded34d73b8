import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';

/// صفحة سلة التسوق
class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final List<Map<String, dynamic>> cartItems = [
    {
      'id': 1,
      'name': 'عدسات أكيوفيو اليومية',
      'brand': '<PERSON> & Johnson',
      'price': 25000,
      'originalPrice': 30000,
      'quantity': 2,
      'image': 'assets/images/lens1.jpg',
    },
    {
      'id': 2,
      'name': 'عدسات بايوفينيتي الشهرية',
      'brand': 'CooperVision',
      'price': 45000,
      'originalPrice': 50000,
      'quantity': 1,
      'image': 'assets/images/lens2.jpg',
    },
    {
      'id': 3,
      'name': 'عدسات ملونة زرقاء',
      'brand': 'FreshLook',
      'price': 35000,
      'originalPrice': 40000,
      'quantity': 1,
      'image': 'assets/images/lens3.jpg',
    },
  ];

  double get subtotal {
    return cartItems.fold(0, (sum, item) => sum + (item['price'] * item['quantity']));
  }

  double get shipping => 5000;
  double get tax => subtotal * 0.1;
  double get total => subtotal + shipping + tax;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.shoppingCart,
          style: TextStyle(
            fontSize: AppDimensions.fontLg,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (cartItems.isNotEmpty)
            TextButton(
              onPressed: _clearCart,
              child: Text(
                'مسح الكل',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: AppDimensions.fontSm,
                ),
              ),
            ),
        ],
      ),
      body: cartItems.isEmpty ? _buildEmptyCart() : _buildCartContent(),
      bottomNavigationBar: cartItems.isNotEmpty ? _buildCheckoutButton() : null,
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80.w,
            color: AppColors.grey400,
          ),
          SizedBox(height: AppDimensions.verticalLg),
          Text(
            AppStrings.emptyCart,
            style: TextStyle(
              fontSize: AppDimensions.fontLg,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.verticalMd),
          ElevatedButton(
            onPressed: () {
              // الانتقال لصفحة التسوق
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.lg,
                vertical: AppDimensions.sm,
              ),
            ),
            child: Text(
              'ابدأ التسوق',
              style: TextStyle(
                fontSize: AppDimensions.fontMd,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.all(AppDimensions.md),
            itemCount: cartItems.length,
            itemBuilder: (context, index) {
              return _buildCartItem(cartItems[index], index);
            },
          ),
        ),
        _buildOrderSummary(),
      ],
    );
  }

  Widget _buildCartItem(Map<String, dynamic> item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.md),
      padding: EdgeInsets.all(AppDimensions.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // صورة المنتج
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusSm),
            ),
            child: Icon(
              Icons.visibility,
              size: AppDimensions.iconLg,
              color: AppColors.grey400,
            ),
          ),

          SizedBox(width: AppDimensions.md),

          // معلومات المنتج
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name'],
                  style: TextStyle(
                    fontSize: AppDimensions.fontMd,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: AppDimensions.verticalXs),
                Text(
                  item['brand'],
                  style: TextStyle(
                    fontSize: AppDimensions.fontSm,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: AppDimensions.verticalSm),
                Row(
                  children: [
                    Text(
                      '${item['price']} د.ع',
                      style: TextStyle(
                        fontSize: AppDimensions.fontMd,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(width: AppDimensions.sm),
                    if (item['originalPrice'] > item['price'])
                      Text(
                        '${item['originalPrice']}',
                        style: TextStyle(
                          fontSize: AppDimensions.fontSm,
                          color: AppColors.textSecondary,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          // أزرار التحكم في الكمية
          Column(
            children: [
              // زر الحذف
              IconButton(
                onPressed: () => _removeItem(index),
                icon: Icon(
                  Icons.delete_outline,
                  color: AppColors.error,
                  size: AppDimensions.iconSm,
                ),
              ),
              
              // أزرار الكمية
              Row(
                children: [
                  IconButton(
                    onPressed: () => _decreaseQuantity(index),
                    icon: Icon(
                      Icons.remove,
                      size: AppDimensions.iconSm,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.grey200,
                      minimumSize: Size(32.w, 32.w),
                    ),
                  ),
                  Container(
                    width: 40.w,
                    child: Text(
                      '${item['quantity']}',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: AppDimensions.fontMd,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _increaseQuantity(index),
                    icon: Icon(
                      Icons.add,
                      size: AppDimensions.iconSm,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: AppColors.surface,
                      minimumSize: Size(32.w, 32.w),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.md),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusLg),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSummaryRow(AppStrings.subtotal, '${subtotal.toInt()} د.ع'),
          _buildSummaryRow(AppStrings.shipping, '${shipping.toInt()} د.ع'),
          _buildSummaryRow(AppStrings.tax, '${tax.toInt()} د.ع'),
          Divider(height: AppDimensions.verticalLg),
          _buildSummaryRow(
            AppStrings.total,
            '${total.toInt()} د.ع',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimensions.verticalXs),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? AppDimensions.fontLg : AppDimensions.fontMd,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? AppDimensions.fontLg : AppDimensions.fontMd,
              fontWeight: FontWeight.bold,
              color: isTotal ? AppColors.primary : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckoutButton() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.md),
      child: ElevatedButton(
        onPressed: () {
          // الانتقال لصفحة الدفع
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.surface,
          padding: EdgeInsets.symmetric(vertical: AppDimensions.md),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
          ),
        ),
        child: Text(
          AppStrings.checkout,
          style: TextStyle(
            fontSize: AppDimensions.fontLg,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _increaseQuantity(int index) {
    setState(() {
      cartItems[index]['quantity']++;
    });
  }

  void _decreaseQuantity(int index) {
    if (cartItems[index]['quantity'] > 1) {
      setState(() {
        cartItems[index]['quantity']--;
      });
    }
  }

  void _removeItem(int index) {
    setState(() {
      cartItems.removeAt(index);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حذف المنتج من السلة')),
    );
  }

  void _clearCart() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح السلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                cartItems.clear();
              });
              Navigator.pop(context);
            },
            child: Text(
              AppStrings.confirm,
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
