import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';

/// الصفحة الرئيسية للتطبيق
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // شريط التطبيق العلوي
              _buildAppBar(),

              // شريط البحث
              _buildSearchBar(),

              // بانر العروض
              _buildOffersBanner(),

              // الفئات الرئيسية
              _buildCategoriesSection(),

              // المنتجات المميزة
              _buildFeaturedProducts(),

              // الأكثر مبيعاً
              _buildBestSellers(),

              // أحدث المنتجات
              _buildLatestProducts(),

              SizedBox(height: AppDimensions.verticalXl),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.md),
      child: Row(
        children: [
          // تحية المستخدم
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً بك',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSm,
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  'أحمد محمد',
                  style: TextStyle(
                    fontSize: AppDimensions.fontLg,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),

          // أيقونة الإشعارات
          Container(
            margin: EdgeInsets.only(left: AppDimensions.sm),
            child: Stack(
              children: [
                IconButton(
                  onPressed: () {
                    // فتح صفحة الإشعارات
                  },
                  icon: Icon(
                    Icons.notifications_outlined,
                    size: AppDimensions.iconMd,
                    color: AppColors.textPrimary,
                  ),
                ),
                // نقطة الإشعارات الجديدة
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // أيقونة السلة
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  // فتح صفحة السلة
                },
                icon: Icon(
                  Icons.shopping_cart_outlined,
                  size: AppDimensions.iconMd,
                  color: AppColors.textPrimary,
                ),
              ),
              // عداد المنتجات في السلة
              Positioned(
                right: 6,
                top: 6,
                child: Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: const BoxDecoration(
                    color: AppColors.accent,
                    shape: BoxShape.circle,
                  ),
                  constraints: BoxConstraints(
                    minWidth: 16.w,
                    minHeight: 16.w,
                  ),
                  child: Text(
                    '3',
                    style: TextStyle(
                      color: AppColors.surface,
                      fontSize: AppDimensions.fontXs,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.md),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'ابحث عن العدسات...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            onPressed: () {
              // فتح صفحة الفلاتر
            },
            icon: const Icon(Icons.tune),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: AppColors.grey100,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.md,
            vertical: AppDimensions.sm,
          ),
        ),
      ),
    );
  }

  Widget _buildOffersBanner() {
    return Container(
      margin: EdgeInsets.all(AppDimensions.md),
      height: 150.h,
      decoration: BoxDecoration(
        gradient: AppColors.accentGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLg),
      ),
      child: Stack(
        children: [
          Positioned(
            right: AppDimensions.md,
            top: AppDimensions.md,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عرض خاص',
                  style: TextStyle(
                    fontSize: AppDimensions.fontLg,
                    fontWeight: FontWeight.bold,
                    color: AppColors.surface,
                  ),
                ),
                SizedBox(height: AppDimensions.verticalSm),
                Text(
                  'خصم 30% على جميع\nالعدسات الملونة',
                  style: TextStyle(
                    fontSize: AppDimensions.fontMd,
                    color: AppColors.surface,
                  ),
                ),
                SizedBox(height: AppDimensions.verticalSm),
                ElevatedButton(
                  onPressed: () {
                    // الانتقال لصفحة العروض
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.surface,
                    foregroundColor: AppColors.accent,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.md,
                      vertical: AppDimensions.sm,
                    ),
                  ),
                  child: Text(
                    'تسوق الآن',
                    style: TextStyle(
                      fontSize: AppDimensions.fontSm,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: AppDimensions.md,
            bottom: 0,
            child: Icon(
              Icons.visibility,
              size: 80.w,
              color: AppColors.surface.withOpacity(0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesSection() {
    final categories = [
      {'name': 'عدسات يومية', 'icon': Icons.today},
      {'name': 'عدسات شهرية', 'icon': Icons.calendar_month},
      {'name': 'عدسات ملونة', 'icon': Icons.palette},
      {'name': 'عدسات طبية', 'icon': Icons.medical_services},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(AppDimensions.md),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الفئات الرئيسية',
                style: TextStyle(
                  fontSize: AppDimensions.fontLg,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              TextButton(
                onPressed: () {
                  // الانتقال لصفحة جميع الفئات
                },
                child: Text(
                  'عرض الكل',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSm,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 100.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return Container(
                width: 80.w,
                margin: EdgeInsets.only(left: AppDimensions.sm),
                child: Column(
                  children: [
                    Container(
                      width: 60.w,
                      height: 60.w,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
                      ),
                      child: Icon(
                        category['icon'] as IconData,
                        size: AppDimensions.iconLg,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: AppDimensions.verticalSm),
                    Text(
                      category['name'] as String,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: AppDimensions.fontXs,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedProducts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(AppDimensions.md),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'المنتجات المميزة',
                style: TextStyle(
                  fontSize: AppDimensions.fontLg,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              TextButton(
                onPressed: () {
                  // الانتقال لصفحة المنتجات المميزة
                },
                child: Text(
                  'عرض الكل',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSm,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 250.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
            itemCount: 5,
            itemBuilder: (context, index) {
              return _buildProductCard();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBestSellers() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(AppDimensions.md),
          child: Text(
            'الأكثر مبيعاً',
            style: TextStyle(
              fontSize: AppDimensions.fontLg,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        SizedBox(
          height: 250.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
            itemCount: 5,
            itemBuilder: (context, index) {
              return _buildProductCard();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLatestProducts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(AppDimensions.md),
          child: Text(
            'أحدث المنتجات',
            style: TextStyle(
              fontSize: AppDimensions.fontLg,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),
        SizedBox(
          height: 250.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.md),
            itemCount: 5,
            itemBuilder: (context, index) {
              return _buildProductCard();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProductCard() {
    return Container(
      width: 160.w,
      margin: EdgeInsets.only(left: AppDimensions.sm),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المنتج
          Container(
            height: 120.h,
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(AppDimensions.radiusMd),
              ),
            ),
            child: Stack(
              children: [
                Center(
                  child: Icon(
                    Icons.visibility,
                    size: AppDimensions.iconXl,
                    color: AppColors.grey400,
                  ),
                ),
                Positioned(
                  top: AppDimensions.sm,
                  right: AppDimensions.sm,
                  child: Container(
                    padding: EdgeInsets.all(AppDimensions.xs),
                    decoration: const BoxDecoration(
                      color: AppColors.surface,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.favorite_outline,
                      size: AppDimensions.iconSm,
                      color: AppColors.grey500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // معلومات المنتج
          Padding(
            padding: EdgeInsets.all(AppDimensions.sm),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عدسات أكيوفيو اليومية',
                  style: TextStyle(
                    fontSize: AppDimensions.fontSm,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: AppDimensions.verticalXs),
                Text(
                  'Johnson & Johnson',
                  style: TextStyle(
                    fontSize: AppDimensions.fontXs,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: AppDimensions.verticalSm),
                Row(
                  children: [
                    Text(
                      '25,000 د.ع',
                      style: TextStyle(
                        fontSize: AppDimensions.fontSm,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(width: AppDimensions.xs),
                    Text(
                      '30,000',
                      style: TextStyle(
                        fontSize: AppDimensions.fontXs,
                        color: AppColors.textSecondary,
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppDimensions.verticalSm),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: AppDimensions.iconXs,
                      color: AppColors.accent,
                    ),
                    SizedBox(width: AppDimensions.xs),
                    Text(
                      '4.5',
                      style: TextStyle(
                        fontSize: AppDimensions.fontXs,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: EdgeInsets.all(AppDimensions.xs),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusXs),
                      ),
                      child: Icon(
                        Icons.add,
                        size: AppDimensions.iconSm,
                        color: AppColors.surface,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
