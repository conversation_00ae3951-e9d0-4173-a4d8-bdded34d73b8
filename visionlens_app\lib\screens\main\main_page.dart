import 'package:flutter/material.dart';
import 'package:visionlens_app/app_properties.dart';
import 'package:visionlens_app/custom_background.dart';
import 'package:visionlens_app/models/contact_lens.dart';
import 'package:visionlens_app/models/category.dart';
import 'components/custom_bottom_bar.dart';
import 'components/product_list.dart';
import 'components/category_grid.dart';

class MainPage extends StatefulWidget {
  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage>
    with TickerProviderStateMixin<MainPage> {
  late TabController bottomTabController;
  String selectedTimeline = 'المنتجات المميزة';
  List<String> timelines = ['المنتجات المميزة', 'الأكثر مبيعاً', 'الأحدث'];

  @override
  void initState() {
    super.initState();
    bottomTabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    bottomTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: CustomBottomBar(controller: bottomTabController),
      body: CustomPaint(
        painter: MainBackground(),
        child: TabBarView(
          controller: bottomTabController,
          physics: const NeverScrollableScrollPhysics(),
          children: <Widget>[
            _buildHomePage(),
            _buildCategoriesPage(),
            _buildCartPage(),
            _buildProfilePage(),
          ],
        ),
      ),
    );
  }

  Widget _buildHomePage() {
    return SafeArea(
      child: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverToBoxAdapter(child: _buildAppBar()),
            SliverToBoxAdapter(child: _buildWelcomeSection()),
            SliverToBoxAdapter(child: _buildSearchBar()),
            SliverToBoxAdapter(child: _buildPromoBanner()),
            SliverToBoxAdapter(child: _buildQuickCategories()),
            SliverToBoxAdapter(child: _buildTimelineSelector()),
          ];
        },
        body: ProductList(
          products: _getProductsByTimeline(),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      padding: const EdgeInsets.all(spacingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          IconButton(
            onPressed: () {
              // Open notifications
            },
            icon: const Icon(Icons.notifications_outlined),
            iconSize: 28,
          ),
          Column(
            children: [
              Text(
                'مرحباً بك في',
                style: TextStyle(
                  fontSize: fontS,
                  color: textSecondary,
                ),
              ),
              Text(
                'VisionLens',
                style: TextStyle(
                  fontSize: fontXL,
                  fontWeight: FontWeight.bold,
                  color: primaryBlue,
                ),
              ),
            ],
          ),
          IconButton(
            onPressed: () {
              // Open search
            },
            icon: const Icon(Icons.search),
            iconSize: 28,
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: spacingM),
      padding: const EdgeInsets.all(spacingL),
      decoration: BoxDecoration(
        gradient: primaryGradient,
        borderRadius: BorderRadius.circular(radiusLarge),
        boxShadow: cardShadow,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'اكتشف عالم العدسات',
                  style: TextStyle(
                    fontSize: fontXL,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: spacingS),
                Text(
                  'أفضل العدسات اللاصقة بأسعار مميزة',
                  style: TextStyle(
                    fontSize: fontM,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                const SizedBox(height: spacingM),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to products
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: primaryBlue,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(radiusMedium),
                    ),
                  ),
                  child: const Text('تسوق الآن'),
                ),
              ],
            ),
          ),
          const SizedBox(width: spacingM),
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(radiusLarge),
            ),
            child: const Icon(
              Icons.visibility,
              size: 40,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(spacingM),
      padding: const EdgeInsets.symmetric(horizontal: spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(radiusLarge),
        boxShadow: softShadow,
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'ابحث عن العدسات...',
          border: InputBorder.none,
          icon: Icon(Icons.search, color: textSecondary),
          suffixIcon: Icon(Icons.filter_list, color: textSecondary),
        ),
      ),
    );
  }

  Widget _buildPromoBanner() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: spacingM),
      height: 120,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [warningOrange, Color(0xFFFFB74D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(radiusLarge),
      ),
      child: Stack(
        children: [
          Positioned(
            right: spacingM,
            top: spacingM,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'خصم 30%',
                  style: TextStyle(
                    fontSize: fontXXL,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  'على جميع العدسات الملونة',
                  style: TextStyle(
                    fontSize: fontM,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: spacingM,
            bottom: spacingM,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: spacingM,
                vertical: spacingS,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(radiusMedium),
              ),
              child: Text(
                'تسوق الآن',
                style: TextStyle(
                  color: warningOrange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickCategories() {
    final categories = LensCategoryData.getCategories().take(4).toList();
    
    return Container(
      margin: const EdgeInsets.all(spacingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفئات الرئيسية',
            style: TextStyle(
              fontSize: fontXL,
              fontWeight: FontWeight.bold,
              color: textPrimary,
            ),
          ),
          const SizedBox(height: spacingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: categories.map((category) {
              return _buildCategoryItem(category);
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(LensCategory category) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: category.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(radiusLarge),
          ),
          child: Icon(
            category.icon,
            color: category.color,
            size: 30,
          ),
        ),
        const SizedBox(height: spacingS),
        Text(
          category.name,
          style: TextStyle(
            fontSize: fontXS,
            color: textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTimelineSelector() {
    return Container(
      margin: const EdgeInsets.all(spacingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: timelines.map((timeline) {
          final isSelected = timeline == selectedTimeline;
          return GestureDetector(
            onTap: () {
              setState(() {
                selectedTimeline = timeline;
              });
            },
            child: Text(
              timeline,
              style: TextStyle(
                fontSize: isSelected ? fontL : fontM,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? primaryBlue : textSecondary,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<ContactLens> _getProductsByTimeline() {
    // This would normally come from an API or database
    return [
      ContactLens(
        id: '1',
        name: 'عدسات أكيوفيو اليومية',
        brand: 'Johnson & Johnson',
        description: 'عدسات لاصقة يومية مريحة وآمنة',
        price: 25000,
        originalPrice: 30000,
        imageUrl: 'assets/images/headphones_2.png',
        images: ['assets/images/headphones_2.png'],
        category: 'يومية',
        type: 'daily',
        material: 'سيليكون هيدروجيل',
        availablePowers: ['-1.00', '-2.00', '-3.00'],
        availableColors: ['شفاف'],
        rating: 4.5,
        reviewCount: 127,
        isInStock: true,
        stockQuantity: 50,
        isFeatured: true,
        specifications: {},
        features: ['مريحة', 'آمنة', 'سهلة الاستخدام'],
      ),
    ];
  }

  Widget _buildCategoriesPage() {
    return const Center(child: Text('صفحة الفئات'));
  }

  Widget _buildCartPage() {
    return const Center(child: Text('صفحة السلة'));
  }

  Widget _buildProfilePage() {
    return const Center(child: Text('صفحة الملف الشخصي'));
  }
}
