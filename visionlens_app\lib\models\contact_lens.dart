class ContactLens {
  final String id;
  final String name;
  final String brand;
  final String description;
  final double price;
  final double originalPrice;
  final String imageUrl;
  final List<String> images;
  final String category;
  final String type; // daily, weekly, monthly, yearly
  final String material;
  final List<String> availablePowers;
  final List<String> availableColors;
  final double rating;
  final int reviewCount;
  final bool isInStock;
  final int stockQuantity;
  final bool isFeatured;
  final bool isOnSale;
  final DateTime? saleEndDate;
  final Map<String, dynamic> specifications;
  final List<String> features;

  ContactLens({
    required this.id,
    required this.name,
    required this.brand,
    required this.description,
    required this.price,
    required this.originalPrice,
    required this.imageUrl,
    required this.images,
    required this.category,
    required this.type,
    required this.material,
    required this.availablePowers,
    required this.availableColors,
    required this.rating,
    required this.reviewCount,
    required this.isInStock,
    required this.stockQuantity,
    this.isFeatured = false,
    this.isOnSale = false,
    this.saleEndDate,
    required this.specifications,
    required this.features,
  });

  // Calculate discount percentage
  double get discountPercentage {
    if (originalPrice <= price) return 0;
    return ((originalPrice - price) / originalPrice * 100);
  }

  // Check if product is on sale
  bool get hasDiscount => discountPercentage > 0;

  // Get formatted price
  String get formattedPrice => '${price.toStringAsFixed(0)} د.ع';
  String get formattedOriginalPrice => '${originalPrice.toStringAsFixed(0)} د.ع';

  // Factory constructor for creating from JSON
  factory ContactLens.fromJson(Map<String, dynamic> json) {
    return ContactLens(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      brand: json['brand'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      originalPrice: (json['originalPrice'] ?? 0).toDouble(),
      imageUrl: json['imageUrl'] ?? '',
      images: List<String>.from(json['images'] ?? []),
      category: json['category'] ?? '',
      type: json['type'] ?? '',
      material: json['material'] ?? '',
      availablePowers: List<String>.from(json['availablePowers'] ?? []),
      availableColors: List<String>.from(json['availableColors'] ?? []),
      rating: (json['rating'] ?? 0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      isInStock: json['isInStock'] ?? true,
      stockQuantity: json['stockQuantity'] ?? 0,
      isFeatured: json['isFeatured'] ?? false,
      isOnSale: json['isOnSale'] ?? false,
      saleEndDate: json['saleEndDate'] != null 
          ? DateTime.parse(json['saleEndDate']) 
          : null,
      specifications: json['specifications'] ?? {},
      features: List<String>.from(json['features'] ?? []),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brand': brand,
      'description': description,
      'price': price,
      'originalPrice': originalPrice,
      'imageUrl': imageUrl,
      'images': images,
      'category': category,
      'type': type,
      'material': material,
      'availablePowers': availablePowers,
      'availableColors': availableColors,
      'rating': rating,
      'reviewCount': reviewCount,
      'isInStock': isInStock,
      'stockQuantity': stockQuantity,
      'isFeatured': isFeatured,
      'isOnSale': isOnSale,
      'saleEndDate': saleEndDate?.toIso8601String(),
      'specifications': specifications,
      'features': features,
    };
  }

  // Copy with method for updating properties
  ContactLens copyWith({
    String? id,
    String? name,
    String? brand,
    String? description,
    double? price,
    double? originalPrice,
    String? imageUrl,
    List<String>? images,
    String? category,
    String? type,
    String? material,
    List<String>? availablePowers,
    List<String>? availableColors,
    double? rating,
    int? reviewCount,
    bool? isInStock,
    int? stockQuantity,
    bool? isFeatured,
    bool? isOnSale,
    DateTime? saleEndDate,
    Map<String, dynamic>? specifications,
    List<String>? features,
  }) {
    return ContactLens(
      id: id ?? this.id,
      name: name ?? this.name,
      brand: brand ?? this.brand,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      images: images ?? this.images,
      category: category ?? this.category,
      type: type ?? this.type,
      material: material ?? this.material,
      availablePowers: availablePowers ?? this.availablePowers,
      availableColors: availableColors ?? this.availableColors,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isInStock: isInStock ?? this.isInStock,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      saleEndDate: saleEndDate ?? this.saleEndDate,
      specifications: specifications ?? this.specifications,
      features: features ?? this.features,
    );
  }
}
