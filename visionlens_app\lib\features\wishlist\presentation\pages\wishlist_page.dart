import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:visionlens_app/core/constants/app_colors.dart';
import 'package:visionlens_app/core/constants/app_strings.dart';
import 'package:visionlens_app/core/constants/app_dimensions.dart';

/// صفحة المفضلة
class WishlistPage extends StatefulWidget {
  const WishlistPage({super.key});

  @override
  State<WishlistPage> createState() => _WishlistPageState();
}

class _WishlistPageState extends State<WishlistPage> {
  final List<Map<String, dynamic>> wishlistItems = [
    {
      'id': 1,
      'name': 'عدسات أكيوفيو اليومية',
      'brand': '<PERSON> & Johnson',
      'price': 25000,
      'originalPrice': 30000,
      'rating': 4.5,
      'image': 'assets/images/lens1.jpg',
      'inStock': true,
    },
    {
      'id': 2,
      'name': 'عدسات بايوفينيتي الشهرية',
      'brand': 'CooperVision',
      'price': 45000,
      'originalPrice': 50000,
      'rating': 4.8,
      'image': 'assets/images/lens2.jpg',
      'inStock': true,
    },
    {
      'id': 3,
      'name': 'عدسات ملونة خضراء',
      'brand': 'FreshLook',
      'price': 35000,
      'originalPrice': 40000,
      'rating': 4.2,
      'image': 'assets/images/lens3.jpg',
      'inStock': false,
    },
    {
      'id': 4,
      'name': 'عدسات أير أوبتيكس',
      'brand': 'Alcon',
      'price': 55000,
      'originalPrice': 60000,
      'rating': 4.7,
      'image': 'assets/images/lens4.jpg',
      'inStock': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          AppStrings.wishlist,
          style: TextStyle(
            fontSize: AppDimensions.fontLg,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (wishlistItems.isNotEmpty)
            TextButton(
              onPressed: _clearWishlist,
              child: Text(
                'مسح الكل',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: AppDimensions.fontSm,
                ),
              ),
            ),
        ],
      ),
      body: wishlistItems.isEmpty ? _buildEmptyWishlist() : _buildWishlistContent(),
    );
  }

  Widget _buildEmptyWishlist() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_outline,
            size: 80.w,
            color: AppColors.grey400,
          ),
          SizedBox(height: AppDimensions.verticalLg),
          Text(
            'قائمة المفضلة فارغة',
            style: TextStyle(
              fontSize: AppDimensions.fontLg,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.verticalMd),
          Text(
            'أضف منتجاتك المفضلة لتجدها هنا',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: AppDimensions.fontMd,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.verticalLg),
          ElevatedButton(
            onPressed: () {
              // الانتقال لصفحة التسوق
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.lg,
                vertical: AppDimensions.sm,
              ),
            ),
            child: Text(
              'تصفح المنتجات',
              style: TextStyle(
                fontSize: AppDimensions.fontMd,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWishlistContent() {
    return GridView.builder(
      padding: EdgeInsets.all(AppDimensions.md),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppDimensions.md,
        mainAxisSpacing: AppDimensions.md,
        childAspectRatio: 0.7,
      ),
      itemCount: wishlistItems.length,
      itemBuilder: (context, index) {
        return _buildWishlistItem(wishlistItems[index], index);
      },
    );
  }

  Widget _buildWishlistItem(Map<String, dynamic> item, int index) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMd),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المنتج
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.grey100,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(AppDimensions.radiusMd),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.visibility,
                      size: AppDimensions.iconXl,
                      color: AppColors.grey400,
                    ),
                  ),
                  
                  // زر إزالة من المفضلة
                  Positioned(
                    top: AppDimensions.sm,
                    right: AppDimensions.sm,
                    child: GestureDetector(
                      onTap: () => _removeFromWishlist(index),
                      child: Container(
                        padding: EdgeInsets.all(AppDimensions.xs),
                        decoration: const BoxDecoration(
                          color: AppColors.surface,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.favorite,
                          size: AppDimensions.iconSm,
                          color: AppColors.error,
                        ),
                      ),
                    ),
                  ),

                  // حالة التوفر
                  if (!item['inStock'])
                    Positioned(
                      bottom: AppDimensions.sm,
                      left: AppDimensions.sm,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.sm,
                          vertical: AppDimensions.xs,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.error,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusXs),
                        ),
                        child: Text(
                          AppStrings.outOfStock,
                          style: TextStyle(
                            fontSize: AppDimensions.fontXs,
                            color: AppColors.surface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // معلومات المنتج
          Expanded(
            flex: 2,
            child: Padding(
              padding: EdgeInsets.all(AppDimensions.sm),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المنتج
                  Text(
                    item['name'],
                    style: TextStyle(
                      fontSize: AppDimensions.fontSm,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: AppDimensions.verticalXs),

                  // العلامة التجارية
                  Text(
                    item['brand'],
                    style: TextStyle(
                      fontSize: AppDimensions.fontXs,
                      color: AppColors.textSecondary,
                    ),
                  ),

                  SizedBox(height: AppDimensions.verticalSm),

                  // التقييم
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: AppDimensions.iconXs,
                        color: AppColors.accent,
                      ),
                      SizedBox(width: AppDimensions.xs),
                      Text(
                        '${item['rating']}',
                        style: TextStyle(
                          fontSize: AppDimensions.fontXs,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // السعر
                  Row(
                    children: [
                      Text(
                        '${item['price']} د.ع',
                        style: TextStyle(
                          fontSize: AppDimensions.fontSm,
                          fontWeight: FontWeight.bold,
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(width: AppDimensions.xs),
                      if (item['originalPrice'] > item['price'])
                        Text(
                          '${item['originalPrice']}',
                          style: TextStyle(
                            fontSize: AppDimensions.fontXs,
                            color: AppColors.textSecondary,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                    ],
                  ),

                  SizedBox(height: AppDimensions.verticalSm),

                  // زر إضافة للسلة
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: item['inStock'] ? () => _addToCart(item) : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: item['inStock'] 
                            ? AppColors.primary 
                            : AppColors.grey300,
                        foregroundColor: AppColors.surface,
                        padding: EdgeInsets.symmetric(vertical: AppDimensions.xs),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusXs),
                        ),
                      ),
                      child: Text(
                        item['inStock'] ? AppStrings.addToCart : AppStrings.outOfStock,
                        style: TextStyle(
                          fontSize: AppDimensions.fontXs,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _removeFromWishlist(int index) {
    final item = wishlistItems[index];
    setState(() {
      wishlistItems.removeAt(index);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم حذف ${item['name']} من المفضلة'),
        action: SnackBarAction(
          label: 'تراجع',
          onPressed: () {
            setState(() {
              wishlistItems.insert(index, item);
            });
          },
        ),
      ),
    );
  }

  void _addToCart(Map<String, dynamic> item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${item['name']} للسلة'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _clearWishlist() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح المفضلة'),
        content: const Text('هل أنت متأكد من مسح جميع المنتجات من المفضلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(AppStrings.cancel),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                wishlistItems.clear();
              });
              Navigator.pop(context);
            },
            child: Text(
              AppStrings.confirm,
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
